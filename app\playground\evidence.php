<?php
require_once __DIR__ . '/../_base/base.php';

$jsonFile = APP_PATH . 'scoping/page_templates/evidence/evidence_en.json';
$message = '';
$messageType = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $result = addEvidence($_POST);
                $message = $result['message'];
                $messageType = $result['type'];
                break;
            case 'edit':
                $result = editEvidence($_POST);
                $message = $result['message'];
                $messageType = $result['type'];
                break;
            case 'delete':
                $result = deleteEvidence($_POST['id']);
                $message = $result['message'];
                $messageType = $result['type'];
                break;
        }
    }
}

// Load current evidence data
function loadEvidenceData() {
    global $jsonFile;
    if (!file_exists($jsonFile)) {
        return [];
    }
    $jsonData = file_get_contents($jsonFile);
    return json_decode($jsonData, true) ?: [];
}

// Save evidence data
function saveEvidenceData($data) {
    global $jsonFile;
    $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents($jsonFile, $jsonData) !== false;
}

// Add new evidence
function addEvidence($data) {
    $evidenceData = loadEvidenceData();
    
    // Find next available ID
    $maxId = 0;
    foreach ($evidenceData as $item) {
        if ($item['id'] > $maxId) {
            $maxId = $item['id'];
        }
    }
    
    $newEvidence = [
        'id' => $maxId + 1,
        'type' => trim($data['type']),
        'text' => trim($data['text']),
        'subtext' => trim($data['subtext']),
        'link' => trim($data['link'])
    ];
    
    $evidenceData[] = $newEvidence;
    
    if (saveEvidenceData($evidenceData)) {
        return ['message' => 'Evidence added successfully!', 'type' => 'success'];
    } else {
        return ['message' => 'Error saving evidence data.', 'type' => 'danger'];
    }
}

// Edit existing evidence
function editEvidence($data) {
    $evidenceData = loadEvidenceData();
    $id = intval($data['id']);
    
    foreach ($evidenceData as &$item) {
        if ($item['id'] == $id) {
            $item['type'] = trim($data['type']);
            $item['text'] = trim($data['text']);
            $item['subtext'] = trim($data['subtext']);
            $item['link'] = trim($data['link']);
            break;
        }
    }
    
    if (saveEvidenceData($evidenceData)) {
        return ['message' => 'Evidence updated successfully!', 'type' => 'success'];
    } else {
        return ['message' => 'Error updating evidence data.', 'type' => 'danger'];
    }
}

// Delete evidence
function deleteEvidence($id) {
    $evidenceData = loadEvidenceData();
    $id = intval($id);
    
    $evidenceData = array_filter($evidenceData, function($item) use ($id) {
        return $item['id'] != $id;
    });
    
    // Re-index array
    $evidenceData = array_values($evidenceData);
    
    if (saveEvidenceData($evidenceData)) {
        return ['message' => 'Evidence deleted successfully!', 'type' => 'success'];
    } else {
        return ['message' => 'Error deleting evidence.', 'type' => 'danger'];
    }
}

$evidenceData = loadEvidenceData();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evidence Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Evidence Manager</h1>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Add New Evidence Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Add New Evidence</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="add">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Type</label>
                                <select class="form-select" name="type" required>
                                    <option value="science">Science</option>
                                    <option value="report">Report</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="link" class="form-label">Link</label>
                                <input type="url" class="form-control" name="link" placeholder="https://example.com">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="text" class="form-label">Text</label>
                        <textarea class="form-control" name="text" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="subtext" class="form-label">Subtext</label>
                        <input type="text" class="form-control" name="subtext" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Add Evidence</button>
                </form>
            </div>
        </div>

        <!-- Existing Evidence List -->
        <div class="card">
            <div class="card-header">
                <h3>Existing Evidence</h3>
            </div>
            <div class="card-body">
                <?php if (empty($evidenceData)): ?>
                    <p class="text-muted">No evidence entries found.</p>
                <?php else: ?>
                    <?php foreach ($evidenceData as $evidence): ?>
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span><strong>ID: <?php echo $evidence['id']; ?></strong> - <?php echo ucfirst($evidence['type']); ?></span>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editEvidence(<?php echo htmlspecialchars(json_encode($evidence)); ?>)">Edit</button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteEvidence(<?php echo $evidence['id']; ?>)">Delete</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <p><strong>Text:</strong> <?php echo htmlspecialchars($evidence['text']); ?></p>
                                <p><strong>Subtext:</strong> <?php echo htmlspecialchars($evidence['subtext']); ?></p>
                                <p><strong>Link:</strong> 
                                    <?php if ($evidence['link']): ?>
                                        <a href="<?php echo htmlspecialchars($evidence['link']); ?>" target="_blank"><?php echo htmlspecialchars($evidence['link']); ?></a>
                                    <?php else: ?>
                                        <em>No link</em>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" id="editForm">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="editId">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Evidence</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editType" class="form-label">Type</label>
                            <select class="form-select" name="type" id="editType" required>
                                <option value="science">Science</option>
                                <option value="report">Report</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editText" class="form-label">Text</label>
                            <textarea class="form-control" name="text" id="editText" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editSubtext" class="form-label">Subtext</label>
                            <input type="text" class="form-control" name="subtext" id="editSubtext" required>
                        </div>
                        <div class="mb-3">
                            <label for="editLink" class="form-label">Link</label>
                            <input type="url" class="form-control" name="link" id="editLink">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Evidence</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editEvidence(evidence) {
            document.getElementById('editId').value = evidence.id;
            document.getElementById('editType').value = evidence.type;
            document.getElementById('editText').value = evidence.text;
            document.getElementById('editSubtext').value = evidence.subtext;
            document.getElementById('editLink').value = evidence.link;
            
            var editModal = new bootstrap.Modal(document.getElementById('editModal'));
            editModal.show();
        }
        
        function deleteEvidence(id) {
            if (confirm('Are you sure you want to delete this evidence?')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = '<input type="hidden" name="action" value="delete"><input type="hidden" name="id" value="' + id + '">';
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
