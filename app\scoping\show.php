<?php

define('IS_DISABLE_LOGIN_CHECK', true);

include '../_base/base.php';

ini_set('display_errors', '0');
ini_set('display_startup_errors', '0');
error_reporting(0);

$scopingManager = new ScopingManager();

if( (isset($_GET['scoping_id']) && $_GET['scoping_id']) && (isset($_GET['secret']) && $_GET['secret']) ) {
    $result = $scopingManager->findScopingByIdAndSecret( $_GET['scoping_id'] , $_GET['secret'] );

    //print_r($result);
    //exit;

    if( ! $result )
        exit;

}else {
    checkUserLogin();
}

$clients = $dbManager->findAll('clients');
$users = $dbManager->findAll('users');

include APP_PATH . '/layouts/head.php';




// Get scopingId from URL parameter or use the first available scoping
$scopingId = isset($_GET['scoping_id']) ? intval($_GET['scoping_id']) : null;


// Get scoping and project data for use in JavaScript
$currentScoping = null;
$currentProject = null;
if (!empty($scopingId)) {
    $currentScoping = $scopingManager->findScopingById($scopingId);
    if (!empty($currentScoping['project_id'])) {
        $projectManager = new ProjectManager();
        $currentProject = $projectManager->findProjectById($currentScoping['project_id']);
    }
}



?>

<body>
<style>
    html, body {
        margin: 0;
        padding: 0;
    }



    .page-entry {
        width: 210mm;
        height: 297mm;
        background-color: #FFF; /* dein Grün */
        position: relative;
        page-break-after: always;
        margin: auto;
        padding:0;
        box-shadow: #CCC 0 0 10px;
    }

    .page-entry h1 {
        color:#0C665A;
    }

    .page-content {
        padding: 2cm 1cm 1cm 2.5cm;
        /*font-family: Arial, sans-serif;*/
    }

    .page-footer {
        position:absolute;
        right: 30px;
        bottom: 30px;
        color: #989898;
        font-size: 18px;
    }

    @page {
        size: A4;
        margin: 0; /* versucht Ränder zu entfernen */
        padding:0;
    }

    @media print {
        body {
            margin: 0;
            padding: 0;
            -webkit-print-color-adjust: initial;
            print-color-adjust: initial;
        }

        .page-entry {
            width: 210mm;
            height: 297mm;
            page-break-after: always;
            margin: 0;
            padding: 0;
            box-shadow:none;
        }

        .page-wrapper {
            margin: 0;
            padding: 0;
            overflow: hidden;
            display: inline-block;
        }

        .page-entry:last-child {
            page-break-after: avoid;
            height: 296mm;
        }

        @page {
            size: A4;
            margin: 0; /* versucht Ränder zu entfernen */
            padding:0;
        }

        .page {
            margin: 0; /* versucht Ränder zu entfernen */
            padding:0;
            display: inline-block;
        }
    }

    .page-break {
        page-break-before: always;
        break-before: always;
    }
</style>

<div class="page">

    <div class="page-wrapper">


        <?php
        if( ! isset($_GET['secret']) ) {
           // include APP_PATH . 'layouts/nav.php';
        }

        ?>

        <div class="page-header d-print-none">
            <div class="container-narrow">
                <div class="row g-2 align-items-center">
                    <div class="col">
                        <!-- Page pre-title -->
                        <div class="page-pretitle">Scoping</div>
                        <h2 class="page-title" style="max-width:500px;">
                            <?php if (!empty($currentProject['info_data']['name'])): ?>
                                <?php echo htmlspecialchars($currentProject['info_data']['name']); ?>

                            <?php else: ?>
                                Scoping Dokument
                            <?php endif; ?>
                        </h2>
                        <?php if (!empty($scopingId)): ?>
                            <small class="text-muted">ID: <?php echo $scopingId; ?></small>
                        <?php endif; ?>
                    </div>
                    <!-- Page title actions -->
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">
                            <?php if (!empty($scopingId)): ?>
                                <!-- Print Button -->
                                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2">
                                        <polyline points="6,9 6,2 18,2 18,9"></polyline>
                                        <path d="M6,18H4a2,2 0 0,1-2-2V11a2,2 0 0,1,2-2H20a2,2 0 0,1,2,2v5a2,2 0 0,1-2,2H18"></path>
                                        <polyline points="6,14 6,22 18,22 18,14"></polyline>
                                    </svg>
                                    Drucken
                                </button>
                            <?php endif; ?>

                        </div>
                        <!-- BEGIN MODAL -->
                        <!-- END MODAL -->
                    </div>
                </div>
            </div>
        </div>

        <br class="d-print-none">
        <br class="d-print-none">

        <!-- PAGE ENTRIES -->
        <?php
        // Only render pages if we have a valid scopingId
        if (!empty($scopingId)) {
            // Get rendered scoping pages from templates

            if( isset($_GET['type']) && $_GET['type'] == 'offer' ) {
                $renderedPages = $scopingManager->getRenderedOfferPages($scopingId);
            }
            elseif( isset($_GET['type']) && $_GET['type'] == 'shipping_label' ) {
                $renderedPages = $scopingManager->getRenderedShippingLabelPage( $scopingId );
            }
            elseif( isset($_GET['type']) && $_GET['type'] == 'evidence' ) {
                $renderedPages = $scopingManager->getRenderedEvidencePages( $scopingId );
            }
            else {
                $renderedPages = $scopingManager->getRenderedScopingPages($scopingId);
            }


            // Echo each rendered page separated with page break
            foreach ($renderedPages as $index => $renderedPage) {
                echo $renderedPage;

                // Add page break between pages (except for the last one)
                if ($index < count($renderedPages) - 1) {
                    echo '<br class="d-print-none">';
                }
            }
        } else {
            echo '<div class="alert alert-warning">Kein Scoping gefunden. Bitte erstellen Sie zuerst ein Scoping.</div>';
        }
        ?>

    </div>



</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/masonry/4.2.2/masonry.pkgd.min.js" defer></script>

<?php
//include APP_PATH . 'layouts/footer.php';
//include APP_PATH . 'layouts/scripts.php';

function evidence( $id ) {
    // Load the evidence data from JSON file
    $jsonFile = APP_PATH . 'scoping/page_templates/evidence/evidence_en.json';

    if (!file_exists($jsonFile)) {
        return '<div class="alert alert-danger">Evidence data file not found.</div>';
    }

    $jsonData = file_get_contents($jsonFile);
    $evidenceData = json_decode($jsonData, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        return '<div class="alert alert-danger">Error parsing evidence data.</div>';
    }

    // Find the evidence item by ID
    $evidenceItem = null;
    foreach ($evidenceData as $item) {
        if ($item['id'] == $id) {
            $evidenceItem = $item;
            break;
        }
    }

    if (!$evidenceItem) {
        return '<div class="alert alert-warning">Evidence item with ID ' . htmlspecialchars($id) . ' not found.</div>';
    }

    $type = 'ev';

    if( $evidenceItem['type'] == 'report' ) {
        $type = '';
    }

    // Shorten subtext to max 50 characters
    $subtext = $evidenceItem['subtext'];
    if (strlen($subtext) > 45) {
        $subtext = substr($subtext, 0, 42) . '...';
    }

    // Generate HTML with variables populated
    $html = '
                <div class="col-md-12 col-lg-4">
                    <div class="card">
                        <div class="card-body p-2">
                            <p class="text-secondary '.$type.'">
                                ' . htmlspecialchars($evidenceItem['text']) . '
                            </p>
                        </div>
                        <div class="card-footer p-2 '.$type.'">' . htmlspecialchars($subtext) . ' <a href="' . htmlspecialchars($evidenceItem['link']) . '">[' . htmlspecialchars($evidenceItem['id']) . '↗] </a></div>
                    </div>
                </div>';

    return $html;
}

function infobox( $text ) {
    $html = '
        <div class="col-md-12 col-lg-12 mt-5" >
            <div class="card">
                <div class="card-stamp">
                    <div class="card-stamp-icon bg-blue">
                        <!-- Download SVG icon from http://tabler.io/icons/icon/bell -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="icon icon-tabler icons-tabler-filled icon-tabler-info-square-rounded"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 2l.642 .005l.616 .017l.299 .013l.579 .034l.553 .046c4.687 .455 6.65 2.333 7.166 6.906l.03 .29l.046 .553l.041 .727l.006 .15l.017 .617l.005 .642l-.005 .642l-.017 .616l-.013 .299l-.034 .579l-.046 .553c-.455 4.687 -2.333 6.65 -6.906 7.166l-.29 .03l-.553 .046l-.727 .041l-.15 .006l-.617 .017l-.642 .005l-.642 -.005l-.616 -.017l-.299 -.013l-.579 -.034l-.553 -.046c-4.687 -.455 -6.65 -2.333 -7.166 -6.906l-.03 -.29l-.046 -.553l-.041 -.727l-.006 -.15l-.017 -.617l-.004 -.318v-.648l.004 -.318l.017 -.616l.013 -.299l.034 -.579l.046 -.553c.455 -4.687 2.333 -6.65 6.906 -7.166l.29 -.03l.553 -.046l.727 -.041l.15 -.006l.617 -.017c.21 -.003 .424 -.005 .642 -.005zm0 9h-1l-.117 .007a1 1 0 0 0 0 1.986l.117 .007v3l.007 .117a1 1 0 0 0 .876 .876l.117 .007h1l.117 -.007a1 1 0 0 0 .876 -.876l.007 -.117l-.007 -.117a1 1 0 0 0 -.764 -.857l-.112 -.02l-.117 -.006v-3l-.007 -.117a1 1 0 0 0 -.876 -.876l-.117 -.007zm.01 -3l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007z" /></svg>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-secondary">
                        ' . htmlspecialchars($text) . '
                    </p>
                </div>
            </div>
        </div>';

    return $html;
}

?>

</body>
</html>
